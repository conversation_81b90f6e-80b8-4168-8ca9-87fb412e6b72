# syntax=docker/dockerfile:1.7-labs

FROM ubuntu:22.04

# ARG NPM_TOKEN

RUN set -eux

ENV DEBIAN_FRONTEND=noninteractive

WORKDIR /home/<USER>

RUN echo "Install OS libraries"

## Production:
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    curl gcc make python3-pip python3-venv unzip jq wget tar software-properties-common \
    git build-essential autoconf libffi-dev cmake ninja-build zlib1g-dev \
    libboost-all-dev flex bison && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Ensure git is available in PATH
RUN git --version

## Running on Silicon chip locally:
# RUN apt-get update && apt-get upgrade -y && \
  # apt-get install -y --no-install-recommends \
  # curl gcc g++ make python3-pip python3-venv unzip jq wget tar software-properties-common \
  # git build-essential autoconf libffi-dev cmake ninja-build zlib1g-dev \
  # libboost-all-dev flex bison && \
  # apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Python 3.11
RUN apt-get update && \
    apt-get install -y python3.10 python3.10-distutils python3-pip && \
    curl -sS https://bootstrap.pypa.io/get-pip.py | python3.10

# Install Node.js using NVM
ENV NODE_VERSION=20.18.0
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash && \
  export NVM_DIR=/root/.nvm && \
  . "$NVM_DIR/nvm.sh" && nvm install ${NODE_VERSION} && nvm alias default v${NODE_VERSION} && nvm use default
ENV NVM_DIR=/root/.nvm
ENV PATH="$NVM_DIR/versions/node/v${NODE_VERSION}/bin/:${PATH}"
RUN corepack enable

# Install solc-select
RUN pip3 install solc-select

# Install solc 0.8.24
RUN solc-select install 0.8.24 && solc-select use 0.8.24

# Install slither and hexbytes
RUN pip3 install hexbytes slither-analyzer && slither --version

# Install echidna
# Official release
RUN wget https://github.com/crytic/echidna/releases/download/v2.2.7/echidna-2.2.7-x86_64-linux.tar.gz && \
  tar -xvkf echidna-2.2.7-x86_64-linux.tar.gz && \
  mv echidna /usr/bin/ && rm echidna-2.2.7-x86_64-linux.tar.gz && \
  echidna --version

# Install foundry
RUN curl -L https://foundry.paradigm.xyz | bash && \
  export PATH="$PATH:/root/.foundry/bin" && \
  foundryup
ENV PATH="$PATH:/root/.foundry/bin"

RUN apt install -y glibc-source
# Install Go
## Production:
RUN wget https://go.dev/dl/go1.22.5.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go1.22.5.linux-amd64.tar.gz && \
    rm go1.22.5.linux-amd64.tar.gz
## Running on Silicon chip locally:
# RUN wget https://go.dev/dl/go1.22.5.linux-arm64.tar.gz && \
#   tar -C /usr/local -xzf go1.22.5.linux-arm64.tar.gz && \
#   rm go1.22.5.linux-arm64.tar.gz
ENV PATH="$PATH:/usr/local/go/bin"
RUN go version


RUN export PATH="$PATH:/usr/local/go/bin"
ENV PATH="$PATH:/usr/local/go/bin"
RUN go version

# Install medusa
## Production:
# Latest: 1a73165fcfc304ba86cfbf3f653784bb27f10286 ( v1.3.0 )
RUN git clone https://github.com/crytic/medusa && \
    cd medusa && git config pull.ff false && git checkout 1a73165fcfc304ba86cfbf3f653784bb27f10286 && \
    GOOS=linux GOARCH=amd64 go build && \
    mv medusa /usr/local/bin/ && chmod +x /usr/local/bin/medusa && \
    rm -rf /medusa
## Running on Silicon chip locally:
# RUN git clone https://github.com/crytic/medusa && \
#   cd medusa && git config pull.ff false && git checkout 1a73165fcfc304ba86cfbf3f653784bb27f10286 && \
#   GOOS=linux GOARCH=arm64 go build && \
#   mv medusa /usr/local/bin/ && chmod +x /usr/local/bin/medusa && \
#   rm -rf /medusa
RUN medusa --version

# Install clang for Halmos dependencies
RUN apt-get update && apt-get install -y clang

# Install halmos
RUN echo "Install halmos"
RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
    echo 'export PATH="$PATH:/root/.local/bin"' >> /root/.bashrc && \
    echo 'export PATH="$PATH:/root/.local/bin"' >> /etc/profile && \
    export PATH="$PATH:/root/.local/bin" && \
    uv tool install --python 3.12 halmos && \
    ln -sf /root/.local/bin/halmos /usr/local/bin/halmos

RUN echo "Halmos installed, version:"
RUN halmos --version

# Install AWS CLI
## Production:
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
  unzip awscliv2.zip && ./aws/install && \
  rm -rf awscliv2.zip aws && \
  aws --version
## Running on Silicon chip locally:
# RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip" && \
#   unzip awscliv2.zip && ./aws/install && \
#   rm -rf awscliv2.zip aws && \
#   aws --version

RUN apt-get install -y zip netcat glibc-source

# See: https://github.com/a16z/halmos/blob/main/packages/solvers/Dockerfile
# Install Z3
COPY --from=ghcr.io/z3prover/z3:ubuntu-20.04-bare-z3-sha-770c51a /usr/bin/z3 /usr/local/bin/z3

# Install STP
COPY --from=msoos/stp /usr/local/bin/stp /usr/local/bin/stp

# Install Yices from the release binaries
# WORKDIR /yices
# ARG YICES_VERSION=2.6.4
# RUN wget https://github.com/SRI-CSL/yices2/releases/download/Yices-${YICES_VERSION}/yices-${YICES_VERSION}-x86_64-pc-linux-gnu.tar.gz -O yices.tar.gz && \
#     tar -xzvf yices.tar.gz --strip-components=1 && \
#     mv /yices/bin/* /usr/local/bin/ && \
#     mv /yices/lib/* /usr/local/lib/ && \
#     mv /yices/include/* /usr/local/include/ && \
#     rm -rf /yices

# # # Install cvc5 from release binaries
# WORKDIR /cvc5
# ARG CVC5_VERSION=1.1.2
# RUN wget https://github.com/cvc5/cvc5/releases/download/cvc5-${CVC5_VERSION}/cvc5-Linux-static.zip -O cvc5-Linux-static.zip && \
#     unzip cvc5-Linux-static.zip && \
#     mv cvc5-Linux-static/bin/cvc5 /usr/local/bin/cvc5 && \
#     chmod +x /usr/local/bin/cvc5 && \
#     rm -rf /cvc5

# # Install Bitwuzla
# RUN apt-get install -y python3.11-venv \
#     build-essential \
#     autoconf \
#     libffi-dev \
#     cmake \
#     ninja-build \
#     software-properties-common \
#     g++ zlib1g-dev libboost-all-dev flex bison

# WORKDIR /bitwuzla
# RUN git clone --depth 1 https://github.com/bitwuzla/bitwuzla . && \
#     python3.11 -m venv .venv && \
#     . .venv/bin/activate && \
#     python3.11 -m pip install meson && \
#     ./configure.py && \
#     cd build && \
#     ninja install

# Install Boolector
WORKDIR /boolector
RUN git clone --depth 1 https://github.com/boolector/boolector . && \
    ./contrib/setup-lingeling.sh && \
    ./contrib/setup-btor2tools.sh && \
    ./configure.sh && \
    cd build && \
    make && \
    mv /boolector/build/bin/* /usr/local/bin/ && \
    rm -rf /boolector

WORKDIR /

RUN cd /

# Check that
# Grant the bash scripts the necessary permissions
COPY run_halmos.sh /usr/local/bin/run_halmos.sh
RUN chmod +x /usr/local/bin/run_halmos.sh

# NIX: https://discourse.nixos.org/t/using-nix-managed-software-as-root-on-non-nixos-systems/32441
RUN mkdir -m 0755 /nix && groupadd -r nixbld && chown root /nix && \
    for n in $(seq 1 10); do useradd -c "Nix build user $n" -d /var/empty -g nixbld -G nixbld -M -N -r -s "$(command -v nologin)" "nixbld$n"; done

# Install Nix as the root user and check installation
# RUN mkdir -p /etc/nix && printf "sandbox = false\nfilter-syscalls = false\n" > /etc/nix/nix.conf
# RUN --security=insecure NIX_CONF_DIR=/etc/nix curl -L https://nixos.org/nix/install | sh -s -- --no-daemon
# RUN . "$HOME/.nix-profile/etc/profile.d/nix.sh"
# ENV USER=root

# Install the hello package using Nix
# RUN . "$HOME/.nix-profile/etc/profile.d/nix.sh" && echo "source $HOME/.nix-profile/etc/profile.d/nix.sh" >> "$HOME/.bashrc" && nix-env -iA nixpkgs.hello

# Source Nix environment in .bashrc for future sessions
# RUN echo ". /root/.nix-profile/etc/profile.d/nix.sh" >> /root/.bashrc

# Install the K framework installer utility (kup) using Nix
# RUN /bin/bash -c ". /root/.nix-profile/etc/profile.d/nix.sh && \
#     GC_DONT_GC=1 nix profile install github:runtimeverification/kup#kup \
#     --option extra-substituters 'https://k-framework.cachix.org' \
#     --option extra-trusted-public-keys 'k-framework.cachix.org-1:jeyMXB2h28gpNRjuVkehg+zLj62ma1RnyyopA/20yFE=' \
#     --experimental-features 'nix-command flakes'"

# Most ressource intensive step
# RUN /bin/bash -c ". /root/.nix-profile/etc/profile.d/nix.sh && \
#     . /root/.nix-profile/etc/profile.d/nix.sh && kup install kontrol"

RUN cd /

# Copy the node app
COPY . .

# RUN /bin/bash -c ". /root/.nix-profile/etc/profile.d/nix.sh && \
#     . /root/.nix-profile/etc/profile.d/nix.sh && which kontrol"

USER root

RUN --mount=type=secret,id=npm_token \
  echo "//registry.npmjs.org/:_authToken=$(cat /run/secrets/npm_token)" > .npmrc && \
  yarn install --frozen-lockfile
ENTRYPOINT yarn start "$@"
