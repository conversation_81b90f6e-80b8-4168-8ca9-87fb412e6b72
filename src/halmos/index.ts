import {
  type FuzzingResults,
  type PropertyAndSequence,
  type VmParsingData,
} from "../types/types";
import { captureFuzzingDuration } from "../utils/utils";

//////////////////////////////////////
//          HALMOS                  //
//////////////////////////////////////

let currentBrokenPropertyHalmos = "";
let currentCounterexample = "";
let isCapturingCounterexample = false;

/**
 * Process a single line of HALMOS output and update the job statistics.
 * @param {string} line - A single line from HALMOS output
 * @param {FuzzingResults} jobStats - The job statistics object to update
 */
export const processHalmos = (line: string, jobStats: FuzzingResults): void => {
  const trimmedLine = line.trim();

  // Skip empty lines and ANSI escape sequences
  if (
    !trimmedLine ||
    trimmedLine.includes("\x1b[") ||
    trimmedLine.includes("3[2K")
  ) {
    return;
  }

  // Extract duration from final result line
  if (trimmedLine.includes("Symbolic test result:")) {
    const timeMatch = trimmedLine.match(/time:\s*([\d.]+)s/);
    if (timeMatch) {
      // Convert to format expected by captureFuzzingDuration (e.g., "4s")
      const durationStr = `${Math.floor(parseFloat(timeMatch[1]))}s`;
      const duration = captureFuzzingDuration(durationStr);
      jobStats.duration = duration || durationStr;
    }

    // Extract passed/failed counts
    const passedMatch = trimmedLine.match(/(\d+)\s+passed/);
    const failedMatch = trimmedLine.match(/(\d+)\s+failed/);

    if (passedMatch) {
      jobStats.passed = parseInt(passedMatch[1]);
    }
    if (failedMatch) {
      jobStats.failed = parseInt(failedMatch[1]);
    }

    jobStats.numberOfTests = jobStats.passed + jobStats.failed;
    return;
  }

  // Detect test results - [FAIL] or [TIMEOUT] or [PASS]
  if (trimmedLine.includes("[FAIL]") || trimmedLine.includes("[TIMEOUT]")) {
    const testMatch = trimmedLine.match(
      /\[(?:FAIL|TIMEOUT)\]\s+([^(]+\([^)]*\)(?:\([^)]*\))*)/
    );
    if (testMatch) {
      currentBrokenPropertyHalmos = testMatch[1].trim();
      jobStats.results.push(trimmedLine);

      // Add to broken properties if not already present
      const existingProperty = jobStats.brokenProperties.find(
        (prop) => prop.brokenProperty === currentBrokenPropertyHalmos
      );

      if (!existingProperty) {
        jobStats.brokenProperties.push({
          brokenProperty: currentBrokenPropertyHalmos,
          sequence: "",
        });
      }
    }
    return;
  }

  // Detect counterexample start
  if (trimmedLine === "Counterexample:") {
    isCapturingCounterexample = true;
    currentCounterexample = "Counterexample:\n";
    return;
  }

  // Capture counterexample parameters
  if (isCapturingCounterexample && trimmedLine.includes("=")) {
    currentCounterexample += `${trimmedLine}\n`;

    // Add to traces for compatibility with existing structure
    jobStats.traces.push(trimmedLine);

    // Add to current broken property sequence
    const existingProperty = jobStats.brokenProperties.find(
      (prop) => prop.brokenProperty === currentBrokenPropertyHalmos
    );

    if (existingProperty) {
      existingProperty.sequence += `${trimmedLine}\n`;
    }
    return;
  }

  // End counterexample capture when we hit a new test or result
  if (
    isCapturingCounterexample &&
    (trimmedLine.includes("[FAIL]") ||
      trimmedLine.includes("[TIMEOUT]") ||
      trimmedLine.includes("[PASS]") ||
      trimmedLine.includes("Symbolic test result:"))
  ) {
    isCapturingCounterexample = false;

    // Add end marker to traces and broken property
    jobStats.traces.push("---End Trace---");

    const existingProperty = jobStats.brokenProperties.find(
      (prop) => prop.brokenProperty === currentBrokenPropertyHalmos
    );

    if (
      existingProperty &&
      !existingProperty.sequence.includes("---End Trace---")
    ) {
      existingProperty.sequence += "---End Trace---\n";
    }

    currentCounterexample = "";
    currentBrokenPropertyHalmos = "";
  }

  // Track running tests for progress (optional)
  if (trimmedLine.includes("Running") && trimmedLine.includes("tests for")) {
    const runningMatch = trimmedLine.match(/Running\s+(\d+)\s+tests/);
    if (runningMatch) {
      // This gives us total tests being run, but we'll get actual counts from final result
    }
  }
};

/**
 * Extract property and sequence information from Halmos logs.
 * @param {string} logs - Raw Halmos logs
 * @param {VmParsingData} [vmData] - Optional VM parsing data
 * @returns {PropertyAndSequence[]} Array of property and sequence objects
 */
export function getHalmosPropertyAndSequence(
  logs: string,
  vmData?: VmParsingData
): PropertyAndSequence[] {
  const results: PropertyAndSequence[] = [];
  const lines = logs.split("\n");
  let currentProperty = "";
  let currentSequence: string[] = [];
  let isCapturingCounterexample = false;

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Skip empty lines and ANSI sequences
    if (
      !trimmedLine ||
      trimmedLine.includes("\x1b[") ||
      trimmedLine.includes("3[2K")
    ) {
      continue;
    }

    // Detect failed test
    if (trimmedLine.includes("[FAIL]") || trimmedLine.includes("[TIMEOUT]")) {
      const testMatch = trimmedLine.match(
        /\[(?:FAIL|TIMEOUT)\]\s+([^(]+\([^)]*\)(?:\([^)]*\))*)/
      );
      if (testMatch) {
        currentProperty = testMatch[1].trim();
        currentSequence = [];
      }
      continue;
    }

    // Start capturing counterexample
    if (trimmedLine === "Counterexample:") {
      isCapturingCounterexample = true;
      continue;
    }

    // Capture counterexample parameters
    if (isCapturingCounterexample && trimmedLine.includes("=")) {
      currentSequence.push(trimmedLine);
      continue;
    }

    // End of counterexample or start of new test
    if (
      isCapturingCounterexample &&
      (trimmedLine.includes("[FAIL]") ||
        trimmedLine.includes("[TIMEOUT]") ||
        trimmedLine.includes("[PASS]") ||
        trimmedLine.includes("Symbolic test result:"))
    ) {
      if (currentProperty && currentSequence.length > 0) {
        results.push({
          brokenProperty: currentProperty,
          sequence: currentSequence,
        });
      }
      isCapturingCounterexample = false;
      currentProperty = "";
      currentSequence = [];
    }
  }

  // Handle case where logs end while capturing
  if (currentProperty && currentSequence.length > 0) {
    results.push({
      brokenProperty: currentProperty,
      sequence: currentSequence,
    });
  }

  return results;
}

/**
 * Convert HALMOS logs to Foundry test functions.
 * @param {string} logs - Raw HALMOS logs
 * @param {string} identifier - Test identifier/prefix
 * @param {VmParsingData} [vmData] - Optional VM parsing data
 * @returns {string} Generated Foundry test functions
 */
export function halmosLogsToFunctions(
  logs: string,
  identifier: string,
  vmData?: VmParsingData
): string {
  const propertySequences = getHalmosPropertyAndSequence(logs, vmData);

  if (propertySequences.length === 0) {
    return "// No failed properties found in Halmos logs";
  }

  return propertySequences
    .map((propSeq, index) => {
      const functionName = `test_${propSeq.brokenProperty.replace(
        /[^a-zA-Z0-9_]/g,
        "_"
      )}_${identifier}_${index}`;
      const sequences = Array.isArray(propSeq.sequence)
        ? propSeq.sequence
        : [propSeq.sequence];

      let functionBody = `function ${functionName}() public {\n`;
      functionBody += `    // Counterexample for: ${propSeq.brokenProperty}\n`;

      sequences.forEach((param) => {
        if (typeof param === "string" && param.includes("=")) {
          const [paramName, paramValue] = param.split("=").map((s) => s.trim());

          // Convert Halmos parameter format to Solidity
          let cleanParamName = paramName
            .replace(/^p_/, "")
            .replace(/_[a-f0-9]+_\d+$/, "");

          // Remove type suffix from parameter name (e.g., "a_bool" -> "a")
          cleanParamName = cleanParamName.replace(
            /_(?:bool|uint256|address|int256)$/,
            ""
          );

          const cleanValue = paramValue.replace(/^0x/, "");

          // Determine type based on parameter name patterns
          if (paramName.includes("_bool_")) {
            const boolValue = cleanValue === "01" ? "true" : "false";
            functionBody += `    bool ${cleanParamName} = ${boolValue};\n`;
          } else if (paramName.includes("_uint256_")) {
            functionBody += `    uint256 ${cleanParamName} = 0x${cleanValue};\n`;
          } else if (paramName.includes("_address_")) {
            functionBody += `    address ${cleanParamName} = 0x${cleanValue.padStart(
              40,
              "0"
            )};\n`;
          } else if (paramName.includes("_int256_")) {
            functionBody += `    int256 ${cleanParamName} = int256(0x${cleanValue});\n`;
          } else {
            // Default to uint256 for unknown types
            functionBody += `    uint256 ${cleanParamName} = 0x${cleanValue};\n`;
          }
        }
      });

      functionBody += `    \n    // Call the original function with counterexample values\n`;
      functionBody += `    // ${propSeq.brokenProperty}(${sequences
        .map((s) => {
          if (typeof s === "string" && s.includes("=")) {
            const paramName = s
              .split("=")[0]
              .trim()
              .replace(/^p_/, "")
              .replace(/_[a-f0-9]+_\d+$/, "");
            return paramName;
          }
          return "";
        })
        .filter(Boolean)
        .join(", ")});\n`;
      functionBody += `}`;

      return functionBody;
    })
    .join("\n\n");
}
