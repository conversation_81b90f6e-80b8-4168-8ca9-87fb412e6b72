import { processHalmos, halmosLogsToFunctions } from './index';
import { FuzzingResults } from '../types/types';

// Sample Halmos output based on the generated logs
const sampleHalmosLogs = `
3[2K[⠃] Compiling...3[2K[⠊] Compiling...3[2K[⠒] Compiling...
No files changed, compilation skipped
⠋ Parsing /Users/<USER>/projects/recon/Halmos-Broken-Properties-Example/out
⠋ Parsing /Users/<USER>/projects/recon/Halmos-Broken-Properties-Example/outRunning 1 tests for test/HalmosDirect.t.sol:HalmosDirect
⠋ Parsing /Users/<USER>/projects/recon/Halmos-Broken-Properties-Example/out⠙ Parsing /Users/<USER>/projects/recon/Halmos-Broken-Properties-Example/out⠹ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1⠸ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1⠼ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1Counterexample: 
    p_a_bool_7b2a94e_00 = 0x00
⠼ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1    p_b_bool_6c9fed6_00 = 0x00
⠼ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1[FAIL] check_bool_xor_always_true(bool,bool) (paths: 4, time: 0.33s, bounds: [])
⠴ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1Symbolic test result: 0 passed; 1 failed; time: 0.34s
⠴ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1⠴ check_bool_xor_always_true(bool,bool): [0:00:00] solving queries: 0 / 1
`;

const sampleComplexHalmosLogs = `
Running 3 tests for test/HalmosDirect.t.sol:HalmosDirect
[FAIL] check_uint_multiplication_bound(uint256,uint256) (paths: 3, time: 0.44s, bounds: [])
Counterexample: 
    p_a_uint256_f73da65_00 = 0xffffffffffffffffffffffffffffffffffffffffffffffffffe0000000000003
    p_b_uint256_5ff3489_00 = 0x3ffffffffffff9
[FAIL] check_struct_simple_invariant((uint256,bool)) (paths: 4, time: 0.12s, bounds: [])
Counterexample: 
    p_s.flag_bool_0dc04dc_00 = 0x01
    p_s.value_uint256_c1f0917_00 = 0x00
[TIMEOUT] check_bool_xor_always_true(bool,bool) (paths: 4, time: 0.11s, bounds: [])
Timeout queries saved in: /var/folders/3_/mlyqmvxn3dx7v3f2xx3xmf3r0000gn/T/check_bool_xor_always_true-c8btwv0i-timeout
Symbolic test result: 0 passed; 3 failed; time: 4.54s
`;

function testHalmosParser() {
  console.log('Testing Halmos parser...');
  
  // Test simple case
  const jobStats: FuzzingResults = {
    duration: '',
    coverage: 0,
    failed: 0,
    passed: 0,
    results: [],
    traces: [],
    brokenProperties: [],
    numberOfTests: 0,
  };

  const lines = sampleHalmosLogs.split('\n');
  lines.forEach(line => {
    processHalmos(line, jobStats);
  });

  console.log('Simple test results:');
  console.log('Duration:', jobStats.duration);
  console.log('Passed:', jobStats.passed);
  console.log('Failed:', jobStats.failed);
  console.log('Number of tests:', jobStats.numberOfTests);
  console.log('Broken properties:', jobStats.brokenProperties.length);
  console.log('Traces:', jobStats.traces.length);
  
  if (jobStats.brokenProperties.length > 0) {
    console.log('First broken property:', jobStats.brokenProperties[0]);
  }

  // Test complex case
  const complexJobStats: FuzzingResults = {
    duration: '',
    coverage: 0,
    failed: 0,
    passed: 0,
    results: [],
    traces: [],
    brokenProperties: [],
    numberOfTests: 0,
  };

  const complexLines = sampleComplexHalmosLogs.split('\n');
  complexLines.forEach(line => {
    processHalmos(line, complexJobStats);
  });

  console.log('\nComplex test results:');
  console.log('Duration:', complexJobStats.duration);
  console.log('Passed:', complexJobStats.passed);
  console.log('Failed:', complexJobStats.failed);
  console.log('Number of tests:', complexJobStats.numberOfTests);
  console.log('Broken properties:', complexJobStats.brokenProperties.length);
  console.log('Traces:', complexJobStats.traces.length);

  // Test logs to functions conversion
  console.log('\nTesting logs to functions conversion:');
  const functions = halmosLogsToFunctions(sampleComplexHalmosLogs, 'test_run');
  console.log('Generated functions:');
  console.log(functions);
}

// Run the test
testHalmosParser();
